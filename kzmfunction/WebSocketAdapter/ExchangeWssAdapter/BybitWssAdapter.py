# kzmfunction/WebSocketAdapter/BybitWssAdapter.py

# import asyncio
import json
import websockets
# import time
# import hmac
# import hashlib
from ..WssAdapter import WssAdapter

class BybitWssAdapter(WssAdapter):
    """
    Bybit交易所的WebSocket适配器。
    支持现货和永续合约，能自动构建交易对，并处理多种数据流。
    """

    _BASE_URIS = {
        'spot': "wss://stream.bybit.com/v5/public/spot",
        'swap': "wss://stream.bybit.com/v5/public/linear"
    }
    _EXCHANGE_NAME = "bybit"
    _DEFAULT_QUOTE_CURRENCY = 'usdt'

    _STREAM_NAME_MAP = {
        'best_orderbook': 'orderbook.1',  # 使用orderbook获取买卖盘口数据
        'orderbook': 'orderbook.1',
        'trades': 'publicTrade',
        # kline@... 格式特殊处理, 无需在此映射
    }

    def __init__(self, symbol: str, market_type: str, streams: list, on_message_callback, quote_currency: str = None):
        """
        初始化Bybit WebSocket适配器。

        Args:
            symbol (str): 基础币种, e.g., 'btc'.
            market_type (str): 市场类型, 'spot' or 'swap'.
            streams (list): 需要订阅的标准化数据流列表, e.g., ['orderbook', 'trades', 'kline@1m'].
            on_message_callback (callable): 接收到消息时调用的回调函数。
            quote_currency (str, optional): 计价货币, e.g., 'usdc'. Defaults to 'usdt'.
        """
        super().__init__(symbol, market_type, on_message_callback, quote_currency)
        if market_type not in self._BASE_URIS:
            raise ValueError(f"Unsupported market_type: {market_type}. Must be 'spot' or 'swap'.")
        if not streams:
            raise ValueError("The 'streams' parameter cannot be empty.")
        
        self.streams = streams
        self._ws = None
        
        self._data_transformers = {
            'orderbook.1': self._transform_depth_update,
            'publicTrade': self._transform_trade,
            'kline': self._transform_kline,
            'tickers': self._transform_book_ticker,
        }

    def _construct_symbol(self) -> str:
        """
        构建Bybit的完整交易对，例如 'BTCUSDT'。
        """
        quote = self.quote_currency if self.quote_currency else self._DEFAULT_QUOTE_CURRENCY
        return f"{self.base_symbol.upper()}{quote.upper()}"

    def get_kline_steam_name(self, interval: str = '1m') -> str:
        bybit_interval = interval.replace('m', '')
        return f"kline.{bybit_interval}.{self._symbol}"

    def get_trade_steam_name(self) -> str:
        return f"publicTrade.{self._symbol}"

    def get_book_ticker_steam_name(self) -> str:
        return f"tickers.{self._symbol}"

    def get_depth_steam_name(self, level: int = 1) -> str:
        return f"orderbook.{level}.{self._symbol}"

    def _get_stream_args(self) -> list:
        """
        根据订阅流构建Bybit WebSocket的订阅参数。
        """
        args = []
        for stream in self.streams:
            if stream.startswith('kline@'):
                interval = stream.split('@')[1]
                # Bybit a list of intervals: 1, 3, 5, 15, 30, 60, 120, 240, 360, 720, 'D', 'W', 'M'
                bybit_interval = interval.replace('m', '') # 1m -> 1, 3m -> 3 ...
                args.append(f"kline.{bybit_interval}.{self._symbol}")
            else:
                bybit_stream = self._STREAM_NAME_MAP.get(stream)
                if bybit_stream:
                    args.append(f"{bybit_stream}.{self._symbol}")
                else:
                    print(f"Warning: Unsupported stream name '{stream}' will be ignored.")
        return args

    # --- 数据转换函数 ---
    def _transform_depth_update(self, data: dict) -> dict:
        # 获取买卖盘数据
        bids = data.get('b', [])
        asks = data.get('a', [])

        # 检查是否有有效的买卖盘数据
        if not bids or not asks:
            return None

        # 提取最佳买卖价格和数量
        best_bid = bids[0] if bids else ['0', '0']
        best_ask = asks[0] if asks else ['0', '0']

        return {
            'type': 'best_orderbook',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('t'),
            'bid1_price': best_bid[0],
            'bid1_amount': best_bid[1],
            'ask1_price': best_ask[0],
            'ask1_amount': best_ask[1],
            # 'raw_data': data
        }

    def _transform_trade(self, data: dict) -> dict:
        return {
            'type': 'trades',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('T'),
            'price': data.get('p'),
            'quantity': data.get('v'),
            'is_buyer_maker': data.get('S') == 'Buy', # Bybit: 'Buy' or 'Sell'
            # 'raw_data': data
        }

    def _transform_kline(self, data: dict) -> dict:
        return {
            'type': 'kline',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('start'),
            'interval': data.get('interval'),
            'open': data.get('open'),
            'high': data.get('high'),
            'low': data.get('low'),
            'close': data.get('close'),
            'volume': data.get('volume'),
            # 'raw_data': data
        }

    def _transform_book_ticker(self, data: dict, payload_ts: int = None) -> dict:
        # Bybit 发送增量更新，只有变化的字段才会包含在消息中
        # 我们需要检查所有必需字段是否存在，如果不存在则跳过此消息
        required_fields = ['bid1Price', 'bid1Size', 'ask1Price', 'ask1Size']
        missing_fields = [field for field in required_fields if data.get(field) is None]

        if missing_fields:
            # 如果缺少关键字段，返回 None 表示跳过此消息
            return None

        return {
            'type': 'best_orderbook',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': payload_ts or data.get('ts'),  # 使用 payload 级别的 ts
            'bid1_price': data.get('bid1Price'),
            'bid1_amount': data.get('bid1Size'),
            'ask1_price': data.get('ask1Price'),
            'ask1_amount': data.get('ask1Size'),
            # 'raw_data': data
        }

    def _transform_unknown(self, data: dict) -> dict:
        return {
            'type': 'unknown',
            'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            # 'raw_data': data
        }

    async def _connect(self):
        """
        连接到Bybit的WebSocket服务器并订阅数据流。
        """
        stream_url = self._BASE_URIS[self.market_type]
        print(f"Connecting to {stream_url}...")
        self._ws = await websockets.connect(stream_url)
        print(f"Connected to {stream_url}.")

        # Subscribe to streams
        args = self._get_stream_args()
        if not args:
            raise ValueError("No valid streams to subscribe to.")
            
        sub_message = {
            "op": "subscribe",
            "args": args
        }
        await self._ws.send(json.dumps(sub_message))
        print(f"Sent subscription request for: {args}")

    async def _process_messages(self):
        """
        处理从Bybit WebSocket接收到的消息。
        """
        if not self._ws:
            raise ConnectionError("WebSocket connection is not established.")

        async for message in self._ws:
            try:
                payload = json.loads(message)

                if 'topic' in payload:
                    topic = payload['topic']
                    data = payload.get('data')
                    payload_ts = payload.get('ts')  # 获取 payload 级别的时间戳

                    # Determine the transformer based on the topic
                    transformer_key = None
                    if topic.startswith('orderbook'):
                        transformer_key = 'orderbook.1'
                    elif topic.startswith('publicTrade'):
                        transformer_key = 'publicTrade'
                    elif topic.startswith('kline'):
                        transformer_key = 'kline'
                    elif topic.startswith('tickers'):
                        transformer_key = 'tickers'

                    if transformer_key:
                        transformer = self._data_transformers.get(transformer_key)
                        if transformer:
                            if isinstance(data, list):
                                for item in data:
                                    # 为 tickers 传递额外的时间戳参数
                                    if transformer_key == 'tickers':
                                        transformed_data = transformer(item, payload_ts)
                                    else:
                                        transformed_data = transformer(item)

                                    # 只有当转换结果不为 None 时才调用回调
                                    if transformed_data and self._on_message_callback:
                                        self._on_message_callback(self, transformed_data)
                            else:
                                # 为 tickers 传递额外的时间戳参数
                                if transformer_key == 'tickers':
                                    transformed_data = transformer(data, payload_ts)
                                else:
                                    transformed_data = transformer(data)

                                # 只有当转换结果不为 None 时才调用回调
                                if transformed_data and self._on_message_callback:
                                    self._on_message_callback(self, transformed_data)
                        else:
                            print(f"Warning: Unknown transformer key '{transformer_key}' from Bybit")
                            if self._on_message_callback:
                                self._on_message_callback(self, self._transform_unknown(payload))
                    else:
                        if self._on_message_callback:
                            self._on_message_callback(self, self._transform_unknown(payload))
                elif 'op' in payload and payload.get('op') == 'subscribe':
                    if payload.get('success'):
                        print(f"Successfully subscribed to: {payload.get('ret_msg')}")
                    else:
                        print(f"Failed to subscribe: {payload.get('ret_msg')}")
                else:
                    if self._on_message_callback:
                        self._on_message_callback(self, self._transform_unknown(payload))

            except json.JSONDecodeError:
                print(f"Error decoding JSON from message: {message}")
            except Exception as e:
                print(f"An error occurred while processing message: {e}")

    async def stop(self):
        """
        扩展基类方法，以确保WebSocket连接被关闭。
        """
        await super().stop()
        if self._ws:
            # Bybit does not require an unsubscribe message for public topics
            await self._ws.close()
            self._ws = None
        print(f"BybitWssAdapter for {self._symbol} connection closed.")
