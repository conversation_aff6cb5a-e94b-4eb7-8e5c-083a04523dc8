#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
WebSocket核心组件模块
包含WebSocket套利程序的通用数据结构和管理器
"""

import asyncio
import time
from typing import Dict, Optional, Any
from dataclasses import dataclass
from threading import Lock


@dataclass
class OrderbookData:
    """订单簿数据结构"""
    bid_price: float
    bid_amount: float
    ask_price: float
    ask_amount: float
    timestamp: float
    
    def is_valid(self) -> bool:
        """检查数据是否有效"""
        return all([
            self.bid_price > 0,
            self.bid_amount > 0,
            self.ask_price > 0,
            self.ask_amount > 0,
            self.ask_price > self.bid_price
        ])


@dataclass
class WSConfig:
    """WebSocket配置"""
    reconnect_interval: float = 5.0  # 重连间隔(秒)
    max_reconnect_attempts: int = 10  # 最大重连次数
    ping_interval: float = 30.0  # ping间隔(秒)
    data_timeout: float = 60.0  # 数据超时时间(秒)


class OrderbookCache:
    """订单簿缓存管理器"""
    def __init__(self):
        self._data: Dict[str, OrderbookData] = {}
        self._lock = Lock()
        self._last_update: Dict[str, float] = {}
    
    def update(self, exchange_name: str, data: OrderbookData):
        """更新缓存数据"""
        with self._lock:
            self._data[exchange_name] = data
            self._last_update[exchange_name] = time.time()
    
    def get(self, exchange_name: str) -> Optional[OrderbookData]:
        """获取缓存数据"""
        with self._lock:
            return self._data.get(exchange_name)
    
    def is_data_fresh(self, exchange_name: str, timeout: float = 60.0) -> bool:
        """检查数据是否新鲜"""
        with self._lock:
            last_update = self._last_update.get(exchange_name, 0)
            return time.time() - last_update < timeout
    
    def get_both_fresh(self, exchange_1_name: str, exchange_2_name: str, timeout: float = 60.0) -> tuple:
        """获取两个交易所的新鲜数据"""
        # 简化版本：直接获取最新数据，不要求严格的新鲜度
        data_1 = self.get(exchange_1_name)
        data_2 = self.get(exchange_2_name)

        if data_1 and data_2:
            return data_1, data_2
        else:
            return None, None

    def has_both_exchanges(self) -> bool:
        """检查是否有两个交易所的数据"""
        with self._lock:
            return len(self._data) >= 2

    def clear(self):
        """清空缓存"""
        with self._lock:
            self._data.clear()
            self._last_update.clear()

    def get_all_exchanges(self) -> list:
        """获取所有已缓存的交易所名称"""
        with self._lock:
            return list(self._data.keys())


class WebSocketManager:
    """WebSocket连接管理器"""
    def __init__(self, config: WSConfig):
        self.config = config
        self.adapters: Dict[str, Any] = {}
        self.tasks: Dict[str, asyncio.Task] = {}
        self.reconnect_counts: Dict[str, int] = {}
        self.is_running = False
        
    async def add_adapter(self, exchange_name: str, adapter):
        """添加WebSocket适配器"""
        self.adapters[exchange_name] = adapter
        self.reconnect_counts[exchange_name] = 0
        
    async def start_all(self):
        """启动所有WebSocket连接"""
        self.is_running = True
        for exchange_name, adapter in self.adapters.items():
            self.tasks[exchange_name] = asyncio.create_task(
                self._run_with_reconnect(exchange_name, adapter)
            )
    
    async def stop_all(self):
        """停止所有WebSocket连接"""
        self.is_running = False
        for task in self.tasks.values():
            if not task.done():
                task.cancel()
        
        # 等待所有任务完成
        if self.tasks:
            await asyncio.gather(*self.tasks.values(), return_exceptions=True)
        
        # 停止所有适配器
        for adapter in self.adapters.values():
            try:
                await adapter.stop()
            except Exception as e:
                print(f"Error stopping adapter: {e}")
    
    async def _run_with_reconnect(self, exchange_name: str, adapter):
        """带重连机制的运行适配器"""
        while self.is_running:
            try:
                print(f"Starting WebSocket for {exchange_name}...")
                await adapter.start()
                print(f"WebSocket for {exchange_name} started successfully")
                self.reconnect_counts[exchange_name] = 0  # 重置重连计数

                # 等待适配器任务完成或出错
                if hasattr(adapter, '_task') and adapter._task:
                    try:
                        await adapter._task
                    except asyncio.CancelledError:
                        print(f"WebSocket task for {exchange_name} was cancelled")
                        break
                    except Exception as task_error:
                        print(f"WebSocket task error for {exchange_name}: {task_error}")
                        raise task_error
                else:
                    print(f"Warning: No task found for {exchange_name}")
                    break

            except Exception as e:
                self.reconnect_counts[exchange_name] += 1
                print(f"WebSocket error for {exchange_name}: {e}")

                if self.reconnect_counts[exchange_name] >= self.config.max_reconnect_attempts:
                    print(f"Max reconnection attempts reached for {exchange_name}")
                    break

                print(f"Reconnecting {exchange_name} in {self.config.reconnect_interval} seconds... "
                      f"(Attempt {self.reconnect_counts[exchange_name]}/{self.config.max_reconnect_attempts})")

                await asyncio.sleep(self.config.reconnect_interval)

                # 重新创建适配器实例
                try:
                    await adapter.stop()
                except:
                    pass


def create_wss_adapter(exchange_name: str, symbol: str, on_message_callback, market_type: str = 'swap'):
    """创建WebSocket适配器工厂函数"""
    # 导入适配器类
    from . import (
        BinanceWssAdapter,
        HyperliquidWssAdapter,
        BybitWssAdapter,
        OkxWssAdapter,
        GateWssAdapter,
        BitgetWssAdapter,
        BackpackWssAdapter,
        # KucoinWssAdapter
    )

    streams = ['best_orderbook']  # 订阅最佳买卖价

    # 定义kcoin交易对列表（需要特殊处理的小额代币）
    kcoin_symbols = ['bonk', 'pepe', 'floki', 'shib']

    if exchange_name == 'binance':
        # 对于Binance的kcoin交易对，只有合约市场需要添加1000前缀
        if symbol.lower() in kcoin_symbols and market_type == 'swap':
            binance_symbol = f"1000{symbol}"
        else:
            binance_symbol = symbol

        return BinanceWssAdapter(
            symbol=binance_symbol,
            market_type=market_type,
            streams=streams,
            on_message_callback=on_message_callback
        )
    elif exchange_name == 'hyperliquid':
        # 对于Hyperliquid的kcoin交易对，需要添加k前缀，注意大小写
        if symbol.lower() in kcoin_symbols:
            # 格式：kBONK（小写k + 大写币种名）
            hyperliquid_symbol = f"k{symbol.upper()}"
        else:
            hyperliquid_symbol = symbol.upper()

        return HyperliquidWssAdapter(
            symbol=hyperliquid_symbol,
            market_type=market_type,
            streams=streams,
            on_message_callback=on_message_callback
        )
    elif exchange_name == 'bybit':
        return BybitWssAdapter(
            symbol=symbol,
            market_type=market_type,
            streams=streams,
            on_message_callback=on_message_callback
        )
    elif exchange_name == 'okx':
        return OkxWssAdapter(
            symbol=symbol,
            market_type=market_type,
            streams=streams,
            on_message_callback=on_message_callback
        )
    elif exchange_name == 'gateio':
        return GateWssAdapter(
            symbol=symbol,
            market_type=market_type,
            streams=streams,
            on_message_callback=on_message_callback
        )
    elif exchange_name == 'bitget':
        return BitgetWssAdapter(
            symbol=symbol,
            market_type=market_type,
            streams=streams,
            on_message_callback=on_message_callback
        )
    elif exchange_name == 'backpack':
        return BackpackWssAdapter(
            symbol=symbol,
            market_type=market_type,
            streams=streams,
            on_message_callback=on_message_callback
        )
    elif exchange_name == 'kucoin':
        return KucoinWssAdapter(
            symbol=symbol,
            market_type=market_type,
            streams=streams,
            on_message_callback=on_message_callback
        )
    else:
        raise ValueError(f"Unsupported exchange: {exchange_name}")
