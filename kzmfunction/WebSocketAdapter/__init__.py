# kzmfunction/WebSocketAdapter/__init__.py

# 原有的WebSocket适配器
from .WssAdapter import WssAdapter
from .ExchangeWssAdapter.BinanceWssAdapter import BinanceWssAdapter
from .ExchangeWssAdapter.BybitWssAdapter import BybitWssAdapter
from .ExchangeWssAdapter.OkxWssAdapter import OkxWssAdapter
from .ExchangeWssAdapter.GateWssAdapter import GateWssAdapter
from .ExchangeWssAdapter.BitgetWssAdapter import BitgetWssAdapter
from .ExchangeWssAdapter.HyperliquidWssAdapter import Hyperliquid<PERSON>ssAdapter
from .ExchangeWssAdapter.BackpackWssAdapter import Backpack<PERSON>ss<PERSON>dapter
# from .ExchangeWssAdapter.KucoinWssAdapter import KucoinWssAdapter  # 暂时注释掉
# 新的WebSocket核心组件
from .WebSocketCore import (
    OrderbookData,
    WSConfig,
    OrderbookCache,
    WebSocketManager,
    create_wss_adapter
)

# 回调处理器
from .CallbackHandlers import (
    BaseCallbackHandler,
    ArbitrageCallbackHandler,
    SimpleDisplayCallbackHandler,
    create_callback_handler
)

# 套利管理器
from .ArbitrageManager import (
    ArbitrageManager,
    SimpleArbitrageManager
)

# 显示工具
from .DisplayUtils import (
    Colors,
    print_colored,
    print_red,
    print_green,
    print_yellow,
    print_blue,
    print_trading_status,
    print_order_result,
)

__all__ = [
    # 原有适配器
    "WssAdapter",
    "BinanceWssAdapter",
    "BybitWssAdapter",
    "OkxWssAdapter",
    "GateWssAdapter",
    "BitgetWssAdapter",
    "HyperliquidWssAdapter",
    "BackpackWssAdapter",

    # 核心组件
    "OrderbookData",
    "WSConfig",
    "OrderbookCache",
    "WebSocketManager",
    "create_wss_adapter",

    # 回调处理器
    "BaseCallbackHandler",
    "ArbitrageCallbackHandler",
    "SimpleDisplayCallbackHandler",
    "create_callback_handler",

    # 套利管理器
    "ArbitrageManager",
    "SimpleArbitrageManager",

    # 显示工具
    "Colors",
    "print_colored",
    "print_red",
    "print_green",
    "print_yellow",
    "print_blue",
    "print_progress",
    "print_progress_with_delay",
    "print_progress_with_timestamp",
    "print_progress_fixed_position",
    "print_progress_with_alert",
    "print_fixed_message",
    "print_trading_status",
    "print_order_result",
    "clear_screen",
    "create_progress_header",
    "beep_alert",
    "ProgressMonitor",
    "AdvancedProgressMonitor",
    "create_progress_monitor",
    "create_advanced_progress_monitor",
    "monitor_trading_progress"
]