"""
备注说明：
    
    
    
"""
from .ExchangeAdapter import ExchangeAdapter
from traceback import format_exc
import sys
import os
from math import log
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ..api_config import kucoin_api_config


class KucoinAdapter(ExchangeAdapter):
    def __init__(self, account='<EMAIL>'):
        self._api_config = kucoin_api_config.api_config
        self.account = account
        self.exchange_name = 'kucoin'
        # 创建交易所
        from ccxt import kucoin
        self.exchange = kucoin()
        self.exchange.apiKey = self._api_config[account]['api_key']
        self.exchange.secret = self._api_config[account]['secret_key']
        self.exchange.password = self._api_config[account]['password']
        

    # region =======================================spot
    # 获取最新价格
    def get_spot_last_price(self, symbol):
        """
        返回当前spot最新成交价格
        @param symbol:      eth
        @return:
        {
            "time": *************,
            "sequence": "**********",
            "price": "2549.41",
            "size": "0.0084999",
            "bestBid": "2549.4",
            "bestBidSize": "13.0466665",
            "bestAsk": "2549.41",
            "bestAskSize": "5.6589386"
            }
        """
        return float(self.exchange.publicGetMarketOrderbookLevel1(params={'symbol': symbol.upper() + '-USDT'})['data']['price'])

    # 获取spot盘口买1价
    def get_spot_buy1(self, symbol):
        """
        返回当前spot盘口的买1价格
        @param symbol:      btc
        @return:
        {
            "time": *************,
            "sequence": "**********",
            "price": "2549.41",
            "size": "0.0084999",
            "bestBid": "2549.4",
            "bestBidSize": "13.0466665",
            "bestAsk": "2549.41",
            "bestAskSize": "5.6589386"
        }
        """
        try:
            return float(self.exchange.publicGetMarketOrderbookLevel1(params={'symbol': symbol.upper() + '-USDT'})['data']['bestBid'])
        except:
            print(format_exc())
            return None

    # 获取spot盘口卖1价
    def get_spot_sell1(self, symbol):
        """
        返回当前spot盘口的卖1价格
        @param symbol:      btc
        @return:

        """
        try:
            return float(self.exchange.publicGetMarketOrderbookLevel1(params={'symbol': symbol.upper() + '-USDT'})['data']['bestAsk'])
        except:
            print(format_exc())
            return None

    # 获取spot最优挂单
    def get_spot_best_orderbook(self, symbol):
        """
        获取币对的最优挂单
        :param symbol:
        :return:
        {
            "time": *************,
            "sequence": "**********",
            "price": "2549.41",
            "size": "0.0084999",
            "bestBid": "2549.4",
            "bestBidSize": "13.0466665",
            "bestAsk": "2549.41",
            "bestAskSize": "5.6589386"
            }
        """
        try:
            raw_orderbook = self.exchange.publicGetMarketOrderbookLevel1(
                params={'symbol': symbol.upper() + '-USDT'})['data']
            return {
                'bid': [raw_orderbook['bestBid'], raw_orderbook['bestBidSize']],
                'ask': [raw_orderbook['bestAsk'], raw_orderbook['bestAskSize']]
            }
        except:
            print(format_exc())
            return None

    # 获取spot盘口深度
    def get_spot_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        """
        try:
            raw_orderbook = self.exchange.publicGetMarketOrderbookLevel220(
                params={'symbol': symbol.upper() + '-USDT'})['data']
            return {
                'symbol': symbol,
                'bids': raw_orderbook['bids'][:limit],
                'asks': raw_orderbook['asks'][:limit]
            }
        except:
            print(format_exc())
            return None

    # TODO：
    def get_spot_kline(self):
        pass

    # 获取现货信息 # TODO：暂未统一格式
    def get_spot_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            pass
        except:
            print(format_exc())
            return None

    # TODO:获取下单价格精度
    def get_spot_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        [
            {
                "symbol": "AVA-USDT",
                "name": "AVA-USDT",
                "baseCurrency": "AVA",
                "quoteCurrency": "USDT",
                "feeCurrency": "USDT",
                "market": "USDS",
                "baseMinSize": "0.1",
                "quoteMinSize": "0.1",
                "baseMaxSize": "1********00",
                "quoteMaxSize": "99999999",
                "baseIncrement": "0.01",
                "quoteIncrement": "0.0001",
                "priceIncrement": "0.0001",
                "priceLimitRate": "0.1",
                "minFunds": "0.1",
                "isMarginEnabled": false,
                "enableTrading": true
                },
                ...
        ]
        """
        try:
            # a = self.exchange.publicGetSymbols()['data'][0]
            a = self.exchange.publicGetMarkets(
                params={'symbol': symbol.upper() + '/USDT'})
            print(a)
            exit()
            return int(round((log(1 / float(a), 10)), 0))
        except:
            print(format_exc())
            return None

    # TODO:获取下单数量精度
    def get_spot_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.exchange.public_get_exchangeinfo(params={'symbol': symbol.upper() + 'USDT'})['symbols'][0]['filters'][1]['minQty']), 10), 0))
        except:
            print(format_exc())
            return None

    # TODO:获取下单价格精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_price_tick_size2(self, symbol, base_symbol):
        """
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(round((log(1 / float(self.exchange.public_get_exchangeinfo(params={'symbol': symbol.upper() + base_symbol.upper()})['symbols'][0]['filters'][0]['tickSize']), 10)), 0))
        except:
            print(format_exc())
            return None

    # TODO:获取下单数量精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_amount_tick_size2(self, symbol, base_symbol):
        """
        获取下单数量精度
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.exchange.public_get_exchangeinfo(params={'symbol': symbol.upper() + base_symbol.upper()})['symbols'][0]['filters'][1]['minQty']), 10), 0))
        except:
            print(format_exc())
            return None

    # TODO：获取最小下单数量
    def get_spot_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            # _info = self.exchange.publicGetSymbols(params={'symbol': symbol.upper() + '-USDT'})['data']
            _info = self.exchange.privateGetHfMarginOrderActiveSymbols(params={'symbol': symbol.upper() + '-USDT'})['data']
            for i in _info:
                if i['symbol'] == symbol.upper() + '-USDT':
                    return float(i['quoteMinSize'])
        except:
            print(format_exc())
            return None
        
    # 获取现货币对
    def get_spot_instruments_symbols(self, base_symbol='usdt'):
        """
        :param base_symbol:
        :return:
        """
        try:
            symbol_list = []
            base_symbol_len = len(base_symbol)
            _info = self.exchange.publicGetSymbols()['data']
            symbol_list.extend([i['symbol'].lower().replace('-', '')
                               for i in _info if i['enableTrading']])
            symbol_list = [
                i for i in symbol_list if i[-base_symbol_len:] == base_symbol]
            return symbol_list
        except:
            print(format_exc())
            return None

    # TODO:获取spot账户余额(单币种)
    def get_spot_account_single_asset(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return float(self.exchange.fetch_balance()[symbol.upper()]['free'])
        except:
            print(format_exc())
            return None

    # TODO:获取spot账户余额(多币种)
    def get_spot_account(self):
        """
        返回spot账户余额
        @return:
            {
              "btc": "0.********",
              "bnb": "51.********",
              "usdt": "1498.********",
              "usdc": "0.********",
              "doge": "6925.********",
              "wif": "96802.********"
            }
        """
        try:
            balance = self.exchange.fetch_balance(params={'omitZeroBalances': True})[
                'info']['balances']
            if balance:
                return {i['asset'].lower(): i['free'] for i in balance}
            else:
                return None
        except Exception as e:
            print("BINANCE查询币种余额出错：", e)
            if 'binance 429 Too Many Requests' or 'binance 418' in str(e):
                print('触发币安binance 429 Too Many Requests报警，等待5秒后继续运行！')
                sleep(5)
            return None

    # TODO:获取spot挂单
    def get_spot_open_order(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            open_orders = self.exchange.fetchOpenOrders(
                symbol.upper() + '/USDT')
            return [{
                'order_id': order['id'],
                'symbol': symbol,
                'direction': order['side'],
                'amount': order['amount'],
                'price': order['price'],
                'order_type': order['type'],
                'average_price': '',
                'remain_amount': order['remaining'],
            } for order in open_orders]
        except:
            print(format_exc())
            return None

    # TODO:下单
    def place_spot_order(self, symbol, direction, amount, price=float, order_type='limit', params={'quoteOrderQty': None}):
        try:
            if order_type == 'limit':
                order_info = self.exchange.createOrder(symbol=symbol.upper(
                ) + '/USDT', side=direction, type='LIMIT', price=price, amount=amount)
            elif order_type == 'market':
                # 按拟成交金额下单
                if params['quoteOrderQty']:
                    order_info = self.exchange.createOrder(symbol=symbol.upper(
                    ) + '/USDT', side=direction, type='MARKET', params=params, amount=None)  # amount参数根据币安文档下成交额订单时不需要，ccxt强制需要，bug？
                else:
                    order_info = self.exchange.createOrder(symbol=symbol.upper(
                    ) + '/USDT', side=direction, type='MARKET', amount=amount)
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['id'],
                'symbol': symbol,
                'direction': direction,
                # 在市价单params['quoteOrderQty']情况下，amount表示quoteOrderQty，这里amount显示的数据是quoteOrderQty(usdt)
                'amount': amount,
                'price': order_info['price'],
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # TODO:下单2
    def place_spot_order2(self, symbol, base_symbol, direction, amount, price='', order_type='limit'):
        try:
            if order_type == 'limit':
                order_info = self.exchange.createOrder(symbol=symbol.upper(
                ) + '/' + base_symbol.upper(), side=direction, type='LIMIT', price=price, amount=amount)
            elif order_type == 'market':
                order_info = self.exchange.createOrder(symbol=symbol.upper(
                ) + '/' + base_symbol.upper(), side=direction, type='MARKET', amount=amount)
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['id'],
                'symbol': symbol + '/' + base_symbol,
                'direction': direction,
                'amount': amount,
                'price': order_info['price'],
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # TODO:获取订单信息
    def get_spot_order_info(self, symbol, order_id):
        try:
            order_info = self.exchange.fetchOrder(
                symbol=symbol.upper() + '/USDT', id=order_id)
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['id'],
                'symbol': symbol,
                'direction': order_info['side'],
                'order_type': order_info['type'],
                'amount': order_info['amount'],
                'price': order_info['price'],
                'average_price': order_info['average'],
                'remain_amount': order_info['remaining'],
                'fee': ''
            }
        except:
            print(format_exc())
            return None

    # TODO:获取订单信息2
    def get_spot_order_info2(self, symbol, base_symbol, order_id):
        try:
            order_info = self.exchange.fetchOrder(
                symbol=symbol.upper() + '/' + base_symbol.upper(), id=order_id)
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['id'],
                'symbol': symbol + '/' + base_symbol,
                'direction': order_info['side'],
                'order_type': order_info['type'],
                'amount': order_info['amount'],
                'price': order_info['price'],
                'average_price': order_info['average'],
                'remain_amount': order_info['remaining'],
                'fee': ''
            }
        except:
            print(format_exc())
            return None

    # TODO:取消订单
    def cancel_spot_order(self, symbol, order_id):
        try:
            order_info = self.exchange.cancelOrder(
                symbol=symbol.upper() + '/USDT', id=order_id)
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if order_info['status'] == 'canceled' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # endregion

    # TODO 交割合约
    # region ==============================================future
    # 获取合约当期instrument_id
    def get_instrument_id(self, symbol, margin_type='usdt', contract_type=''):
        """
        根据输入的symbol获取当期周期(当周/次周/季度/次季度)的合约id
        对于季度合约:
            如果不指定contract_type,则返回当周,次周,季度,次季度 四个instrument_id组成的list
            如果指定contract_type,则返回单个instrument_id的str
        @param symbol:  BTC
        @param margin_type:  币本位coin or U本位usdt
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:
        """
        instrument_id = self.exchange.get_products()
        margin_type2 = {'coin': 'USD', 'usdt': 'USDT'}.get(margin_type)
        if contract_type:
            for i in instrument_id:
                if i['underlying_index'] == symbol.upper() and i['alias'] == contract_type and i['quote_currency'] == margin_type2:
                    return i['instrument_id']
        else:
            instrument_id_list = []
            for i in instrument_id:
                if i['underlying_index'] == symbol.upper() and i['quote_currency'] == margin_type2:
                    instrument_id_list.append(i['instrument_id'])
            return instrument_id_list

    # 获取future盘口买1价
    def get_future_buy1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的买1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:

        """
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_depth(instrument_id)['bids'][0][0])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_depth(instrument_id)['bids'][0][0])

    # 获取future盘口卖1价
    def get_future_sell1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的卖1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:

        """
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_depth(instrument_id)['asks'][0][0])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_depth(instrument_id)['asks'][0][0])

    # # 获取future-K线
    # def get_future_kline(self, symbol, margin_type='usdt', contract_type='quarter', period='15min', start='', end=''):
    #     """
    #     获取future-K线
    #     @param symbol:  btc
    #     @param margin_type: 币本位(coin)或U本位(usdt)
    #     @param contract_type:   this_week, next_week, quarter, bi_quarter
    #     @param period:  时间周期, min、hour、day、week  okex输入参数以秒为单位，默认值60。如[60/180/300/900/1800/3600/7200/14400/21600/43200/86400/604800]
    #     @param start:  str
    #     @param end:   str
    #     @return:    future：# 300根K线
    #     """
    #     # 获取K线周期，转为秒
    #     if 'min' in period:
    #         granularity = str(get_number(period) * 60).split('.')[0]
    #     elif 'hour' in period:
    #         granularity = str(get_number(period) * 3600).split('.')[0]
    #     elif 'day' in period:
    #         granularity = str(get_number(period) * 86400).split('.')[0]
    #     elif 'week' in period:
    #         granularity = str(get_number(period) * 604800).split('.')[0]
    #     else:
    #         granularity = '900'  # 默认15min
    #
    #     # 获取起始时间
    #     if start and end:
    #         start = (pd.to_datetime(start) - timedelta(hours=8)).isoformat("T") + "Z"
    #         end = (pd.to_datetime(end) - timedelta(hours=8)).isoformat("T") + "Z"
    #
    #     # =======================================交割合约K线
    #     instrument_id = self.get_instrument_id(symbol, margin_type, contract_type)  # 获取合约id
    #     if margin_type == 'coin':  # 币本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     elif margin_type == 'usdt':  # U本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     else:
    #         kline = pd.DataFrame()
    #     # 转为Dataframe，转为北京时区
    #     kline = pd.DataFrame(kline, columns=['candle_begin_time', 'open', 'high', 'low', 'close', 'cont', 'volume'])
    #     kline['candle_begin_time'] = pd.to_datetime(kline['candle_begin_time']) + timedelta(hours=8)
    #
    #     return kline

    # <editor-fold desc="# ====================== todo future">
    # 获取future账户余额
    def get_future_account(self, symbol, margin_type='usdt'):
        """
        返回future账户的余额（币或USDT）
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        # ======================================= 交割合约:
            币本位：
                {
                  'equity': '105.********',
                  'margin': '106.********',
                  'realized_pnl': '0',
                  'unrealized_pnl': '1.********',
                  'margin_ratio': '0.********',     保证金率
                  'margin_mode': 'crossed',
                  'total_avail_balance': '104.********',
                  'margin_frozen': '106.********',
                  'margin_for_unfilled': '0',
                  'liqui_mode': 'tier',
                  'maint_margin_ratio': '0.02',
                  'liqui_fee_rate': '0.00035',
                  'can_withdraw': '0',  可划转数量
                  'underlying': 'BSV-USD',
                  'currency': 'BSV'
                }
            U本位：
                {
                  'total_avail_balance': '66.********',
                  'contracts': None,
                  'equity': '66.********',
                  'margin_mode': 'fixed',
                  'auto_margin': '0',
                  'liqui_mode': 'tier',
                  'can_withdraw': '66.********',
                  'currency': 'USDT'
                }
        """
        if margin_type == 'coin':  # 币本位
            return self.exchange.get_coin_account(symbol.upper() + '-USD')
        elif margin_type == 'usdt':  # U本位
            return self.exchange.get_coin_account(symbol.upper() + '-USDT')

    # 获取future持仓信息
    def get_future_position(self, symbol, margin_type='usdt'):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:    返回持仓列表
        交割合约：
            币本位：
                [{
                  'long_qty': '4145',
                  'long_avail_qty': '4145',
                  'long_avg_cost': '195.********',
                  'long_settlement_price': '194.08',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_avg_cost': '195.********',
                  'short_settlement_price': '195.********',
                  'liquidation_price': '132.61',
                  'instrument_id': 'BSV-USD-200626',
                  'leverage': '2',
                  'created_at': '2020-04-02T23:37:37.220Z',
                  'updated_at': '2020-04-18T08:00:10.913Z',
                  'margin_mode': 'crossed',
                  'short_margin': '0.0',
                  'short_pnl': '0.0',
                  'short_pnl_ratio': '-0.03896512',
                  'short_unrealised_pnl': '0.0',
                  'long_margin': '104.08296505',
                  'long_pnl': '3.87219199',
                  'long_pnl_ratio': '0.03652354',
                  'long_unrealised_pnl': '5.40579291',
                  'long_settled_pnl': '-1.53360092',
                  'short_settled_pnl': '0',
                  'last': '199.14'
                }]
            U本位：
                [{
                  'long_qty': '0',
                  'long_avail_qty': '0',
                  'long_margin': '0',
                  'long_liqui_price': '0',
                  'long_pnl_ratio': '0',
                  'long_avg_cost': '0',
                  'long_settlement_price': '0',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_margin': '0',
                  'short_liqui_price': '0',
                  'short_pnl_ratio': '0',
                  'short_avg_cost': '0',
                  'short_settlement_price': '0',
                  'instrument_id': 'BSV-USDT-200626',
                  'long_leverage': '10',
                  'short_leverage': '10',
                  'created_at': '1970-01-01T00:00:00.000Z',
                  'updated_at': '1970-01-01T00:00:00.000Z',
                  'margin_mode': 'fixed',
                  'short_margin_ratio': '0',
                  'short_maint_margin_ratio': '0',
                  'short_pnl': '0',
                  'short_unrealised_pnl': '0',
                  'long_margin_ratio': '0',
                  'long_maint_margin_ratio': '0',
                  'long_pnl': '0',
                  'long_unrealised_pnl': '0',
                  'long_settled_pnl': '0',
                  'short_settled_pnl': '0',
                  'last': '199.53'
                }]
        """
        # =======================================交割合约持仓信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取交割合约id

        for i in instrument_id:
            temp_position = self.exchange.get_specific_position(i)['holding']

            if temp_position[0]['long_qty'] != '0' or temp_position[0]['short_qty'] != '0':
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单信息
    def get_future_open_order(self, symbol, margin_type='usdt'):
        """
        返回future账户的挂单信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        交割合约：
            币本位：
            [{'instrument_id': 'BSV-USD-200501',
                  'size': '1',
                  'timestamp': '2020-04-19T07:00:04.943Z',
                  'filled_qty': '0',    # 成交数量
                  'fee': '0',
                  'order_id': '4754217692783617',
                  'price': '190',   委托价格
                  'price_avg': '0',     成交均价
                  'status': '0',
                  'state': '0',     -2：失败-1：撤单成功0：等待成交1：部分成交2：完全成交3：下单中4：撤单中
                  'type': '1',      1:开多2:开空3:平多4:平空
                  'contract_val': '10',     合约面值
                  'leverage': '2',
                  'client_oid': '',
                  'pnl': '0',   收益
                  'order_type': '0'
            }]
            U本位：
                [{'instrument_id': 'BSV-USDT-200501',
                   'size': '1',
                   'timestamp': '2020-04-19T07:15:15.196Z',
                   'filled_qty': '0',
                   'fee': '0',
                   'order_id': '4754277346794497',
                   'price': '190',
                   'price_avg': '0',
                   'status': '0',
                   'state': '0',
                   'type': '1',     1:开多2:开空3:平多4:平空
                   'contract_val': '1',
                   'leverage': '10',
                   'client_oid': '',
                   'pnl': '0',
                   'order_type': '0'
                }]
        """
        # =======================================交割合约挂单信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取合约id

        for i in instrument_id:
            temp_position = self.exchange.get_order_list(i, state='6')[
                'order_info']
            if temp_position:
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单订单号
    def get_future_open_order_id(self, symbol, margin_type):
        """
        返回future账户的挂单订单号
        :param symbol:
        :param margin_type:
        :return:
        """
        # return self.get_future_open_order(symbol, margin_type)
        return self.get_future_open_order(symbol, margin_type)[0]['order_id']

    # TODO 根据订单号获取future订单详细信息
    def get_future_order_info(self, order_id, instrument_id=''):
        """
        @param order_id:
        @param instrument_id:
        @return:
        交割合约订单信息：
            {
              'instrument_id': 'BSV-USD-200626',
              'size': '1',
              'timestamp': '2020-04-22T14:46:12.585Z',
              'filled_qty': '0',    成交数量
              'fee': '0',
              'order_id': '4773037511302145',
              'price': '180',
              'price_avg': '0',
              'status': '0',
              'state': '0',     -2:失败 -1:撤单成功 0:等待成交 1:部分成交 2:完全成交 3:下单中 4:撤单中
              'type': '1',      1:开多 2:开空 3:平多 4:平空
              'contract_val': '10',     合约面值
              'leverage': '1',
              'client_oid': '',
              'pnl': '0',   收益
              'order_type': '0'
            }
        """

        return self.exchange.get_order_info(instrument_id, order_id)

    # 获取future可平仓数量
    def get_future_avail_amount(self, symbol, margin_type='usdt'):
        """
        获取future可平仓数量,只针对持有单一合约有效，若有平仓的挂单，则不含这部分持仓
        @param symbol: btc
        @param margin_type: coin or usdt
        @return:
        """
        order_info = self.get_future_position(symbol, margin_type)[0]
        if order_info['long_avail_qty'] != '0':
            return float(order_info['long_avail_qty'])
        elif order_info['short_avail_qty'] != '0':
            return float(order_info['short_avail_qty'])

    # future下单
    def place_future_order(self, symbol, amount, direction, order_type='limit', margin_type='usdt', contract_type='quarter', price=''):
        """

        @param symbol: btc
        @param amount:
        @param direction: buy,sell,close_buy,close_sell
        @param price:
        @param order_type: limit market
        @param margin_type: coin,usdt
        @param contract_type: this_week, next_week, quarter, bi_quarter
        @return:
        参数名	参数类型	描述
            order_id	String	订单ID，下单失败时，此字段值为-1
            client_oid	String	由您设置的订单ID来识别您的订单
            error_code	String	错误码，下单成功时为0，下单失败时会显示相应错误码
            error_message	String	错误信息，下单成功时为空，下单失败时会显示错误信息
            result	Boolean	调用接口返回结果
        """
        type_dict = {'buy': '1', 'sell': '2',
                     'close_buy': '3', 'close_sell': '4'}
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id

        if order_type == 'limit':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=str(price), size=str(amount))
        elif order_type == 'market':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=price, order_type='4', size=str(amount))
        else:
            order_info = None
        return order_info

    # future 撤单
    def cancel_future_order(self, order_id, instrument_id):
        """

        @param order_id: str 订单号
        @param instrument_id: str
        @return:
        """
        cancel_order_info = self.exchange.revoke_order(
            instrument_id, order_id=order_id)

        return cancel_order_info

    # endregion

    # region ==============================================usdt swap
    # TODO:获取最新价格
    def get_swap_latest_price(self, symbol):
        try:
            pass
        except:
            print(format_exc())
            return None

    # 获取swap盘口买1价
    def get_swap_buy1(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.exchange.futuresPublicGetTicker(params={'symbol': symbol.upper() + 'USDTM'})['data']['bestBidPrice'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.exchange.futuresPublicGetTicker(params={'symbol': symbol.upper() + 'USDTM'})['data']['bestAskPrice'])
        except:
            print(format_exc())
            return None

    # 获取swap最优挂单
    def get_swap_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
        {'bid': ['60896.40', '4.079'], 'ask': ['60896.50', '5.771']}
        """
        try:
            raw_orderbook = self.exchange.futuresPublicGetTicker(
                params={'symbol': symbol.upper() + 'USDTM'})['data']
            return {
                'bid': [raw_orderbook['bestBidPrice'], raw_orderbook['bestBidSize']],
                'ask': [raw_orderbook['bestAskPrice'], raw_orderbook['bestAskSize']]
            }
        except:
            print(format_exc())
            return None

    # 获取swap orderbook
    def get_swap_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:

        """
        try:
            raw_orderbook = self.exchange.futuresPublicGetLevel2Depth20(
                params={'symbol': symbol.upper() + 'USDTM', 'limit': limit})['data']
            return {
                'symbol': symbol,
                'bids': raw_orderbook['bids'][:limit],
                'asks': raw_orderbook['asks'][:limit]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return self.exchange.futuresPublicGetContractsSymbol(params={'symbol': symbol.upper() + 'USDTM'})['data']['fundingFeeRate']
        except:
            print(format_exc())
            return None

    # TODO:获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大1000
        :return: list越往后时间越靠近现在
        """
        try:
            funding_rates = None
            return {
                'symbol': symbol,
                'funding_rate': [i['fundingRate'] for i in funding_rates],
                'funding_time': [i['fundingTime'] for i in funding_rates]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约持仓量(usdt)
    def get_swap_contract_open_interest(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            _info = self.exchange.futuresPublicGetContractsSymbol(
                params={'symbol': symbol.upper() + 'USDTM'})['data']
            return float(_info['openInterest']) * _info['multiplier'] * _info['lastTradePrice']
        except:
            print(format_exc())
            return None

    # # TODO: 获取swap-K线
    def get_swap_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # T获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        {
            "symbol": "ETHUSDTM",
            "rootSymbol": "USDT",
            "type": "FFWCSX",
            "firstOpenDate": 1591086000000,
            "expireDate": "None",
            "settleDate": "None",
            "baseCurrency": "ETH",
            "quoteCurrency": "USDT",
            "settleCurrency": "USDT",
            "maxOrderQty": 1000000,
            "maxPrice": 1000000.0,
            "lotSize": 1,
            "tickSize": 0.01,
            "indexPriceTickSize": 0.01,
            "multiplier": 0.01,
            "initialMargin": 0.01,
            "maintainMargin": 0.005,
            "maxRiskLimit": 100000,
            "minRiskLimit": 100000,
            "riskStep": 50000,
            "makerFeeRate": 0.0002,
            "takerFeeRate": 0.0006,
            "takerFixFee": 0.0,
            "makerFixFee": 0.0,
            "settlementFee": "None",
            "isDeleverage": true,
            "isQuanto": true,
            "isInverse": false,
            "markMethod": "FairPrice",
            "fairMethod": "FundingRate",
            "fundingBaseSymbol": ".ETHINT8H",
            "fundingQuoteSymbol": ".USDTINT8H",
            "fundingRateSymbol": ".ETHUSDTMFPI8H",
            "indexSymbol": ".KETHUSDT",
            "settlementSymbol": "",
            "status": "Open",
            "fundingFeeRate": -8.4e-05,
            "predictedFundingFeeRate": 0.000127,
            "fundingRateGranularity": 28800000,
            "openInterest": "6968316",
            "turnoverOf24h": 311356334.421154,
            "volumeOf24h": 124365.73,
            "markPrice": 2522.14,
            "indexPrice": 2522.0,
            "lastTradePrice": 2522.73,
            "nextFundingRateTime": 163874,
            "maxLeverage": 100,
            "sourceExchanges": [
                "okex",
                "binance",
                "kucoin",
                "gateio",
                "bybit",
                "bitmart",
                "bitget"
            ],
            "premiumsSymbol1M": ".ETHUSDTMPI",
            "premiumsSymbol8H": ".ETHUSDTMPI8H",
            "fundingBaseSymbol1M": ".ETHINT",
            "fundingQuoteSymbol1M": ".USDTINT",
            "lowPrice": 2427.95,
            "highPrice": 2554.94,
            "priceChgPct": 0.0182,
            "priceChg": 45.18,
            "k": 6610.0,
            "m": 4110.0,
            "f": 1.3,
            "mmrLimit": 0.3,
            "mmrLevConstant": 100.0
            }
        """
        try:
            return self.exchange.futuresPublicGetContractsSymbol(params={'symbol': symbol.upper() + 'USDTM'})['data']
        except:
            print(format_exc())
            return None

    # 获取下单价格精度
    def get_swap_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.exchange.futuresPublicGetContractsSymbol(params={'symbol': symbol.upper() + 'USDTM'})['data']['tickSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size(self, symbol):
        """
        获取下单数量精度
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.exchange.futuresPublicGetContractsSymbol(params={'symbol': symbol.upper() + 'USDTM'})['data']['multiplier']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_swap_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            _info = self.exchange.futuresPublicGetContractsSymbol(params={'symbol': symbol.upper() + 'USDTM'})['data']
            return float(_info['lotSize']) * float(_info['multiplier'])
        except:
            print(format_exc())
            return None

    # TODO：获取swap账户保证金率
    def get_swap_margin_rate(self):
        """
        获取账户保证金率
        :return:
        """
        try:
            _info = self.exchange.info.user_state(self.ethereum_address)
            _margin_rate = float(_info['crossMaintenanceMarginUsed']) / float(_info['crossMarginSummary']['accountValue'])
            return _margin_rate
        except:
            print(format_exc())
            return None

    # 获取永续合约币对
    def get_swap_instruments_symbols(self, base_symbol='usdt'):
        """
        :param base_symbol:
        :return:
        """
        try:
            symbol_list = []
            base_symbol_len = len(base_symbol)
            _info = self.exchange.futuresPublicGetContractsActive()['data']
            symbol_list.extend([i['symbol'].replace('M', '').lower() for i in _info if i['status']
                               == 'Open' and i['symbol'].replace('M', '')[-base_symbol_len:] == base_symbol.upper()])
            # 如果'xbt'在symbol_list中，将其替换为'btc' + base_symbol
            if 'xbt' + base_symbol in symbol_list:
                symbol_list.remove('xbt' + base_symbol)
                symbol_list.append('btc' + base_symbol)
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取永续合约账户信息(单一币种,默认usdt)
    def get_swap_account_single_asset(self, symbol='usdt'):
        try:
            return self.exchange.privateGetV5AccountWalletBalance(params={'coin': symbol.upper(), 'accountType': 'UNIFIED'})['result']['list'][0]['coin'][0]['walletBalance']
        except:
            print(format_exc())
            return None

    # TODO:获取swap账户持仓信息
    def get_swap_position(self, symbol=None):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @return:
        永续合约：
            U本位：
              {
                "symbol": "BTCUSDT",
                "positionSide": "BOTH",
                "positionAmt": "-0.005",
                "entryPrice": "60183.7",
                "breakEvenPrice": "60159.62652",
                "markPrice": "60579.********",
                "unRealizedProfit": "-1.********",
                "liquidationPrice": "259122.********",
                "isolatedMargin": "0",
                "notional": "-302.********",
                "marginAsset": "USDT",
                "isolatedWallet": "0",
                "initialMargin": "151.********",
                "maintMargin": "1.21159200",
                "positionInitialMargin": "151.********",
                "openOrderInitialMargin": "0",
                "adl": "1",
                "bidNotional": "0",
                "askNotional": "0",
                "updateTime": "1723209932783"
              }
        """
        try:
            if symbol:
                positions = self.exchange.futuresPrivateGetPosition(
                    params={'symbol': symbol.upper() + 'USDT'})
                print(positions)
                exit()
                return [
                    {
                        'symbol': symbol,
                        'direction': 'buy' if float(i['positionAmt']) > 0 else 'sell',
                        'amount': i['positionAmt'],
                        'price': i['entryPrice'],
                        'liquidation_price': i['liquidationPrice'],
                    } for i in positions
                ]
            else:
                positions = self.exchange.fapiPrivateV3GetPositionRisk()
                return [
                    {
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': 'buy' if float(i['positionAmt']) > 0 else 'sell',
                        'amount': i['positionAmt'],
                        'price': i['entryPrice'],
                        'liquidation_price': i['liquidationPrice'],
                    } for i in positions
                ]
        except:
            print(format_exc())
            return None

    # TODO:获取swap挂单信息
    def get_swap_open_order(self, symbol=None):
        """
        返回swap账户的挂单信息
        @param symbol:  btc
        @return:
        永续合约：
            U本位：
            [
              {
                "orderId": "************",
                "symbol": "BTCUSDT",
                "status": "NEW",
                "clientOrderId": "web_PmaN8HBLN50nrWFJwNBn",
                "price": "60000",
                "avgPrice": "0",
                "origQty": "0.002",
                "executedQty": "0",
                "cumQuote": "0.00000",
                "timeInForce": "GTC",
                "type": "LIMIT",
                "reduceOnly": false,
                "closePosition": false,
                "side": "SELL",
                "positionSide": "BOTH",
                "stopPrice": "0",
                "workingType": "CONTRACT_PRICE",
                "priceProtect": false,
                "origType": "LIMIT",
                "priceMatch": "NONE",
                "selfTradePreventionMode": "NONE",
                "goodTillDate": "0",
                "time": "1723214436305",
                "updateTime": "1723214436305"
              }
            ]
        """
        try:
            if symbol:
                open_orders = self.exchange.fapiPrivateGetOpenOrders(
                    params={'symbol': symbol.upper() + 'USDT'})
                return [
                    {
                        'order_id': i['orderId'],
                        'sym'
                        'direction': i['side'].lower(),
                        'order_type': i['type'].lower(),
                        'amount': i['origQty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': float(i['origQty']) - float(i['executedQty']),
                    } for i in open_orders
                ]
            else:
                # 不带symbol的权重为40 请小心使用不带symbol参数的调用
                open_orders = self.exchange.fapiPrivateGetOpenOrders()
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': i['side'].lower(),
                        'order_type': i['type'].lower(),
                        'amount': i['origQty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': float(i['origQty']) - float(i['executedQty']),
                    } for i in open_orders
                ]
        except:
            print(format_exc())
            return None

    # TODO:获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            return self.exchange.fapiPrivateGetOpenOrders(params={'symbol': symbol.upper() + 'USDT'})[0]['orderId']
        except:
            print(format_exc())
            return None

    # TODO:下单
    def place_swap_order(self, symbol, direction, amount, price=float, order_type='limit', close_position=False):
        try:
            if order_type == 'limit':
                if close_position:
                    order_info = self.exchange.fapiPrivatePostOrder(params={'symbol': symbol.upper() + 'USDT', 'side': direction.upper(), 'type': 'LIMIT',
                                                                            'quantity': amount, 'price': price, 'timeInForce': 'GTC', 'reduceOnly': 'true'})
                else:
                    order_info = self.exchange.fapiPrivatePostOrder(params={'symbol': symbol.upper() + 'USDT', 'side': direction.upper(), 'type': 'LIMIT',
                                                                            'quantity': amount, 'price': price, 'timeInForce': 'GTC'})
            elif order_type == 'market':
                # 自动市价平仓
                if close_position:
                    order_info = self.exchange.fapiPrivatePostOrder(params={'symbol': symbol.upper() + 'USDT', 'side': direction.upper(), 'type': 'MARKET',
                                                                            'quantity': amount, 'reduceOnly': 'true'})
                else:
                    order_info = self.exchange.fapiPrivatePostOrder(params={'symbol': symbol.upper() + 'USDT', 'side': direction.upper(), 'type': 'MARKET',
                                                                            'quantity': amount})
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': direction,
                'amount': amount,
                'price': order_info['price'],
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # TODO:获取订单信息
    def get_swap_order_info(self, symbol, order_id):
        try:
            order_info = self.exchange.fapiPrivateGetOrder(
                params={'symbol': symbol.upper() + 'USDT', 'orderId': order_id})
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': order_info['side'],
                'order_type': order_info['type'],
                'amount': order_info['origQty'],
                'price': order_info['price'],
                'average_price': order_info['avgPrice'],
                'remain_amount': float(order_info['origQty']) - float(order_info['executedQty']),
                'fee': ''
            }
        except:
            print(format_exc())
            return None

    # TODO:撤单
    def cancel_swap_order(self, symbol, order_id):
        try:
            cancel_order_info = self.exchange.fapiPrivateDeleteOrder(
                params={'symbol': symbol.upper() + 'USDT', 'orderId': order_id})
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if cancel_order_info['status'] == 'CANCELED' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # endregion

    # region ==============================================coin swap

	# endregion